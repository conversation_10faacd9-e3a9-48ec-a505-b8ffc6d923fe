# 电脑监控软件

一个功能强大的Windows电脑监控软件，能够实时监控注册表、系统服务、文件系统和临时文件的变化。

## 功能特性

### 🔍 监控功能
- **注册表监控**: 监控重要注册表项的增删改操作
- **服务监控**: 监控系统服务的启动、停止、状态变化
- **文件系统监控**: 监控指定盘符下的文件创建、修改、删除操作
- **临时文件监控**: 监控系统临时目录和用户临时目录的变化

### 📊 界面功能
- **图形界面**: 基于tkinter的直观GUI界面
- **实时显示**: 实时显示监控事件和系统状态
- **事件过滤**: 支持按类型过滤监控事件
- **统计信息**: 详细的监控统计和系统信息

### 📝 日志功能
- **文件日志**: 自动轮转的日志文件记录
- **数据库日志**: SQLite数据库存储事件记录
- **日志导出**: 支持导出日志为JSON格式
- **事件分级**: 按严重程度分类事件（INFO、WARNING、ERROR）

### ⚙️ 配置功能
- **配置文件**: 通过config.ini文件进行配置
- **路径管理**: 可添加/删除监控路径
- **排除规则**: 支持排除特定文件类型和目录
- **性能优化**: 可配置监控间隔和记录数量限制

## 系统要求

- **操作系统**: Windows 7/8/10/11
- **Python版本**: Python 3.7 或更高版本
- **内存**: 至少 512MB RAM
- **磁盘空间**: 至少 100MB 可用空间

## 安装方法

### 方法一：自动安装（推荐）

1. 下载所有文件到一个目录
2. 以管理员身份运行命令提示符
3. 进入软件目录
4. 运行安装脚本：
```bash
python install.py
```

### 方法二：手动安装

1. 安装Python依赖包：
```bash
pip install psutil==5.9.5 watchdog==3.0.0 pywin32==306
```

2. 下载所有源代码文件到同一目录

3. 运行软件：
```bash
python main.py
```

## 使用方法

### GUI模式（推荐）
```bash
python main.py
```
或双击桌面快捷方式

### 控制台模式
```bash
python main.py --mode console
```

### 查看状态
```bash
python main.py --status
```

### 查看最近事件
```bash
python main.py --events 50
```

### 禁用特定监控
```bash
# 禁用注册表监控
python main.py --no-registry

# 禁用服务监控
python main.py --no-services

# 禁用文件系统监控
python main.py --no-filesystem

# 禁用临时文件监控
python main.py --no-temp
```

## 配置说明

编辑 `config.ini` 文件来自定义监控设置：

```ini
[MONITORING]
# 启用/禁用各种监控
enable_registry = true
enable_services = true
enable_filesystem = true
enable_temp_files = true

# 文件系统监控路径（用逗号分隔）
monitor_paths = C:\,D:\

# 排除的文件扩展名
exclude_extensions = .tmp,.log,.cache

# 排除的目录
exclude_directories = $Recycle.Bin,System Volume Information

[LOGGING]
# 日志级别
log_level = INFO
log_file = monitor.log
max_log_size = 10485760
backup_count = 5

[DATABASE]
# 数据库设置
db_file = monitor.db
max_records = 10000
```

## 文件结构

```
电脑监控软件/
├── main.py                 # 主程序入口
├── gui.py                  # 图形界面
├── logger.py               # 日志记录系统
├── registry_monitor.py     # 注册表监控
├── service_monitor.py      # 服务监控
├── filesystem_monitor.py   # 文件系统监控
├── temp_monitor.py         # 临时文件监控
├── config.ini              # 配置文件
├── requirements.txt        # 依赖包列表
├── install.py              # 安装脚本
└── README.md               # 说明文档
```

## 监控说明

### 注册表监控
监控以下重要注册表路径：
- `HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Run`
- `HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Run`
- `HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services`
- 等等...

### 服务监控
- 监控所有Windows服务的状态变化
- 特别关注关键系统服务
- 检测新增或删除的服务

### 文件系统监控
- 监控指定盘符下的文件变化
- 支持递归监控子目录
- 可配置排除规则

### 临时文件监控
监控以下临时目录：
- 系统临时目录
- 用户临时目录
- 浏览器缓存目录
- 回收站

## 安全特性

- **可疑文件检测**: 自动标记可疑的可执行文件
- **关键路径监控**: 重点监控系统关键路径
- **事件分级**: 按严重程度分类安全事件
- **实时警报**: 高危事件实时显示

## 性能优化

- **多线程设计**: 各监控模块独立运行
- **资源控制**: 限制内存和CPU使用
- **智能过滤**: 减少无关事件记录
- **定期清理**: 自动清理旧日志和数据

## 故障排除

### 常见问题

1. **权限不足**
   - 以管理员身份运行程序
   - 确保有足够的系统权限

2. **依赖包安装失败**
   - 检查网络连接
   - 更新pip: `python -m pip install --upgrade pip`
   - 手动安装: `pip install 包名`

3. **监控不工作**
   - 检查配置文件设置
   - 查看日志文件中的错误信息
   - 确认监控路径存在且可访问

4. **界面无法启动**
   - 确认已安装tkinter
   - 尝试控制台模式: `python main.py --mode console`

### 日志文件位置
- 应用日志: `monitor.log`
- 数据库: `monitor.db`
- 配置文件: `config.ini`

## 开发信息

- **开发语言**: Python 3.7+
- **GUI框架**: tkinter
- **数据库**: SQLite
- **文件监控**: watchdog
- **系统信息**: psutil
- **Windows API**: pywin32

## 许可证

本软件仅供学习和研究使用。

## 更新日志

### v1.0 (2024-08-04)
- 初始版本发布
- 实现基本监控功能
- 添加GUI界面
- 完善日志系统

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建Issue报告问题
- 提交Pull Request贡献代码

---

**注意**: 本软件需要管理员权限才能正常监控系统资源。请确保在受信任的环境中使用。
