[MONITORING]
# 监控设置
enable_registry = true
enable_services = true
enable_filesystem = true
enable_temp_files = true

# 文件系统监控路径（用逗号分隔）
monitor_paths = C:\,D:\

# 排除的文件扩展名（用逗号分隔）
exclude_extensions = .tmp,.log,.cache

# 排除的目录（用逗号分隔）
exclude_directories = $Recycle.Bin,System Volume Information,Windows\Temp

[LOGGING]
# 日志设置
log_level = INFO
log_file = monitor.log
max_log_size = 10485760
backup_count = 5

[DATABASE]
# 数据库设置
db_file = monitor.db
max_records = 10000

[GUI]
# 界面设置
window_width = 1200
window_height = 800
refresh_interval = 1000
max_display_records = 1000
