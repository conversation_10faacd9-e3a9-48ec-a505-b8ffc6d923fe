import os
import threading
import time
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from logger import MonitorLogger
import configparser

class FileSystemEventHandler(FileSystemEventHandler):
    def __init__(self, logger, exclude_extensions=None, exclude_directories=None):
        super().__init__()
        self.logger = logger
        self.exclude_extensions = exclude_extensions or []
        self.exclude_directories = exclude_directories or []
    
    def should_ignore(self, path):
        """检查是否应该忽略此路径"""
        # 检查文件扩展名
        _, ext = os.path.splitext(path)
        if ext.lower() in self.exclude_extensions:
            return True
        
        # 检查目录
        for exclude_dir in self.exclude_directories:
            if exclude_dir.lower() in path.lower():
                return True
        
        return False
    
    def on_created(self, event):
        if not event.is_directory and not self.should_ignore(event.src_path):
            file_size = self._get_file_size(event.src_path)
            severity = self._get_file_severity(event.src_path)
            self.logger.log_event('FILESYSTEM', 'File Created', event.src_path, 
                                f'文件大小: {file_size} bytes', severity)
    
    def on_deleted(self, event):
        if not event.is_directory and not self.should_ignore(event.src_path):
            severity = self._get_file_severity(event.src_path)
            self.logger.log_event('FILESYSTEM', 'File Deleted', event.src_path, '', severity)
    
    def on_modified(self, event):
        if not event.is_directory and not self.should_ignore(event.src_path):
            file_size = self._get_file_size(event.src_path)
            severity = self._get_file_severity(event.src_path)
            self.logger.log_event('FILESYSTEM', 'File Modified', event.src_path, 
                                f'文件大小: {file_size} bytes', severity)
    
    def on_moved(self, event):
        if not event.is_directory and not self.should_ignore(event.src_path):
            severity = self._get_file_severity(event.src_path)
            self.logger.log_event('FILESYSTEM', 'File Moved', event.src_path, 
                                f'移动到: {event.dest_path}', severity)
    
    def _get_file_size(self, path):
        """获取文件大小"""
        try:
            return os.path.getsize(path)
        except:
            return 0
    
    def _get_file_severity(self, path):
        """根据文件类型和位置确定严重程度"""
        path_lower = path.lower()
        
        # 可执行文件
        executable_extensions = ['.exe', '.bat', '.cmd', '.com', '.scr', '.pif', '.vbs', '.js', '.jar']
        _, ext = os.path.splitext(path_lower)
        if ext in executable_extensions:
            return 'WARNING'
        
        # 系统关键目录
        critical_paths = ['\\windows\\system32\\', '\\windows\\syswow64\\', '\\program files\\']
        for critical_path in critical_paths:
            if critical_path in path_lower:
                return 'WARNING'
        
        # 启动目录
        startup_paths = ['\\startup\\', '\\start menu\\']
        for startup_path in startup_paths:
            if startup_path in path_lower:
                return 'WARNING'
        
        return 'INFO'

class FileSystemMonitor:
    def __init__(self, logger=None, config_file='config.ini'):
        self.logger = logger or MonitorLogger()
        self.config = configparser.ConfigParser()
        self.config.read(config_file, encoding='utf-8')
        
        self.monitoring = False
        self.observers = []
        
        # 从配置文件读取监控路径
        monitor_paths_str = self.config.get('MONITORING', 'monitor_paths', fallback='C:\\')
        self.monitor_paths = [path.strip() for path in monitor_paths_str.split(',')]
        
        # 从配置文件读取排除项
        exclude_ext_str = self.config.get('MONITORING', 'exclude_extensions', fallback='')
        self.exclude_extensions = [ext.strip() for ext in exclude_ext_str.split(',') if ext.strip()]
        
        exclude_dir_str = self.config.get('MONITORING', 'exclude_directories', fallback='')
        self.exclude_directories = [dir.strip() for dir in exclude_dir_str.split(',') if dir.strip()]
        
        # 统计信息
        self.stats = {
            'files_created': 0,
            'files_deleted': 0,
            'files_modified': 0,
            'files_moved': 0
        }
    
    def start_monitoring(self):
        """开始监控文件系统"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.logger.log_event('FILESYSTEM', 'Monitor', 'Started', 
                            f'文件系统监控已启动，监控路径: {", ".join(self.monitor_paths)}')
        
        # 为每个监控路径创建观察者
        for path in self.monitor_paths:
            if os.path.exists(path):
                try:
                    observer = Observer()
                    event_handler = FileSystemEventHandler(
                        self.logger, 
                        self.exclude_extensions, 
                        self.exclude_directories
                    )
                    
                    observer.schedule(event_handler, path, recursive=True)
                    observer.start()
                    self.observers.append(observer)
                    
                    self.logger.log_event('FILESYSTEM', 'Monitor', 'Path Added', 
                                        f'开始监控路径: {path}')
                    
                except Exception as e:
                    self.logger.log_event('FILESYSTEM', 'Monitor', 'Error', 
                                        f'无法监控路径 {path}: {e}', 'ERROR')
            else:
                self.logger.log_event('FILESYSTEM', 'Monitor', 'Warning', 
                                    f'路径不存在: {path}', 'WARNING')
    
    def stop_monitoring(self):
        """停止监控文件系统"""
        self.monitoring = False
        
        for observer in self.observers:
            try:
                observer.stop()
                observer.join(timeout=5)
            except Exception as e:
                self.logger.log_event('FILESYSTEM', 'Monitor', 'Error', 
                                    f'停止观察者失败: {e}', 'ERROR')
        
        self.observers.clear()
        self.logger.log_event('FILESYSTEM', 'Monitor', 'Stopped', '文件系统监控已停止')
    
    def add_monitor_path(self, path):
        """添加监控路径"""
        if not os.path.exists(path):
            self.logger.log_event('FILESYSTEM', 'Monitor', 'Error', 
                                f'路径不存在: {path}', 'ERROR')
            return False
        
        if path in self.monitor_paths:
            return True
        
        try:
            if self.monitoring:
                observer = Observer()
                event_handler = FileSystemEventHandler(
                    self.logger, 
                    self.exclude_extensions, 
                    self.exclude_directories
                )
                
                observer.schedule(event_handler, path, recursive=True)
                observer.start()
                self.observers.append(observer)
            
            self.monitor_paths.append(path)
            self.logger.log_event('FILESYSTEM', 'Monitor', 'Path Added', 
                                f'添加监控路径: {path}')
            return True
            
        except Exception as e:
            self.logger.log_event('FILESYSTEM', 'Monitor', 'Error', 
                                f'添加监控路径失败 {path}: {e}', 'ERROR')
            return False
    
    def remove_monitor_path(self, path):
        """移除监控路径"""
        if path not in self.monitor_paths:
            return True
        
        try:
            # 找到对应的观察者并停止
            # 注意：这里简化处理，实际应该维护路径和观察者的映射关系
            self.monitor_paths.remove(path)
            self.logger.log_event('FILESYSTEM', 'Monitor', 'Path Removed', 
                                f'移除监控路径: {path}')
            return True
            
        except Exception as e:
            self.logger.log_event('FILESYSTEM', 'Monitor', 'Error', 
                                f'移除监控路径失败 {path}: {e}', 'ERROR')
            return False
    
    def get_disk_usage(self):
        """获取磁盘使用情况"""
        disk_usage = {}
        
        for path in self.monitor_paths:
            try:
                if os.path.exists(path):
                    # 获取磁盘根目录
                    root = os.path.splitdrive(path)[0] + '\\'
                    if root not in disk_usage:
                        total, used, free = os.statvfs(root) if hasattr(os, 'statvfs') else (0, 0, 0)
                        if total == 0:  # Windows系统使用不同的方法
                            import shutil
                            total, used, free = shutil.disk_usage(root)
                        
                        disk_usage[root] = {
                            'total': total,
                            'used': used,
                            'free': free,
                            'percent': (used / total * 100) if total > 0 else 0
                        }
                        
            except Exception as e:
                self.logger.log_event('FILESYSTEM', 'Disk Usage', 'Error', 
                                    f'获取磁盘使用情况失败 {path}: {e}', 'ERROR')
        
        return disk_usage
    
    def get_monitoring_status(self):
        """获取监控状态"""
        return {
            'monitoring': self.monitoring,
            'monitor_paths': self.monitor_paths,
            'active_observers': len(self.observers),
            'exclude_extensions': self.exclude_extensions,
            'exclude_directories': self.exclude_directories,
            'stats': self.stats.copy()
        }
