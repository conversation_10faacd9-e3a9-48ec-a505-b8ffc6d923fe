import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import configparser
from datetime import datetime
import json

from logger import MonitorLogger
from registry_monitor import RegistryMonitor
from service_monitor import ServiceMonitor
from filesystem_monitor import FileSystemMonitor
from temp_monitor import TempFileMonitor

class MonitorGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("电脑监控软件 v1.0")
        self.root.geometry("1200x800")
        
        # 配置
        self.config = configparser.ConfigParser()
        self.config.read('config.ini', encoding='utf-8')
        
        # 初始化组件
        self.logger = MonitorLogger()
        self.registry_monitor = RegistryMonitor(self.logger)
        self.service_monitor = ServiceMonitor(self.logger)
        self.filesystem_monitor = FileSystemMonitor(self.logger)
        self.temp_monitor = TempFileMonitor(self.logger)
        
        # 监控状态
        self.monitoring_status = {
            'registry': False,
            'services': False,
            'filesystem': False,
            'temp_files': False
        }
        
        # 创建界面
        self.create_widgets()
        
        # 启动定时更新
        self.update_display()
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建控制面板
        self.create_control_panel(main_frame)
        
        # 创建标签页
        self.create_notebook(main_frame)
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = ttk.LabelFrame(parent, text="监控控制", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 监控开关
        switches_frame = ttk.Frame(control_frame)
        switches_frame.pack(fill=tk.X)
        
        # 注册表监控
        self.registry_var = tk.BooleanVar()
        ttk.Checkbutton(switches_frame, text="注册表监控", variable=self.registry_var,
                       command=self.toggle_registry_monitor).pack(side=tk.LEFT, padx=(0, 20))
        
        # 服务监控
        self.services_var = tk.BooleanVar()
        ttk.Checkbutton(switches_frame, text="服务监控", variable=self.services_var,
                       command=self.toggle_service_monitor).pack(side=tk.LEFT, padx=(0, 20))
        
        # 文件系统监控
        self.filesystem_var = tk.BooleanVar()
        ttk.Checkbutton(switches_frame, text="文件系统监控", variable=self.filesystem_var,
                       command=self.toggle_filesystem_monitor).pack(side=tk.LEFT, padx=(0, 20))
        
        # 临时文件监控
        self.temp_var = tk.BooleanVar()
        ttk.Checkbutton(switches_frame, text="临时文件监控", variable=self.temp_var,
                       command=self.toggle_temp_monitor).pack(side=tk.LEFT, padx=(0, 20))
        
        # 控制按钮
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="全部启动", command=self.start_all_monitors).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="全部停止", command=self.stop_all_monitors).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="清空日志", command=self.clear_logs).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="导出日志", command=self.export_logs).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="设置", command=self.open_settings).pack(side=tk.RIGHT)
    
    def create_notebook(self, parent):
        """创建标签页"""
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 实时监控标签页
        self.create_realtime_tab()
        
        # 注册表标签页
        self.create_registry_tab()
        
        # 服务标签页
        self.create_services_tab()
        
        # 文件系统标签页
        self.create_filesystem_tab()
        
        # 临时文件标签页
        self.create_temp_files_tab()
        
        # 统计标签页
        self.create_statistics_tab()
    
    def create_realtime_tab(self):
        """创建实时监控标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="实时监控")
        
        # 创建事件列表
        columns = ('时间', '类型', '来源', '操作', '详情', '严重程度')
        self.events_tree = ttk.Treeview(frame, columns=columns, show='headings', height=20)
        
        for col in columns:
            self.events_tree.heading(col, text=col)
            self.events_tree.column(col, width=150)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.events_tree.yview)
        self.events_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.events_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 过滤框架
        filter_frame = ttk.Frame(frame)
        filter_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Label(filter_frame, text="过滤类型:").pack(side=tk.LEFT)
        self.filter_var = tk.StringVar(value="全部")
        filter_combo = ttk.Combobox(filter_frame, textvariable=self.filter_var, 
                                   values=["全部", "REGISTRY", "SERVICE", "FILESYSTEM", "TEMP_FILES"])
        filter_combo.pack(side=tk.LEFT, padx=(5, 10))
        filter_combo.bind('<<ComboboxSelected>>', self.filter_events)
        
        ttk.Button(filter_frame, text="刷新", command=self.refresh_events).pack(side=tk.LEFT)
    
    def create_registry_tab(self):
        """创建注册表标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="注册表监控")
        
        # 状态信息
        status_frame = ttk.LabelFrame(frame, text="监控状态", padding=10)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.registry_status_label = ttk.Label(status_frame, text="状态: 未启动")
        self.registry_status_label.pack()
        
        # 监控的注册表路径
        paths_frame = ttk.LabelFrame(frame, text="重要监控路径", padding=10)
        paths_frame.pack(fill=tk.BOTH, expand=True)
        
        paths_text = tk.Text(paths_frame, height=15, wrap=tk.WORD)
        paths_scrollbar = ttk.Scrollbar(paths_frame, orient=tk.VERTICAL, command=paths_text.yview)
        paths_text.configure(yscrollcommand=paths_scrollbar.set)
        
        paths_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        paths_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 添加重要路径信息
        important_paths = [
            "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run",
            "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run",
            "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce",
            "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services",
            "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Winlogon",
            "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Policies",
            "HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies"
        ]
        
        paths_text.insert(tk.END, "正在监控以下重要注册表路径:\n\n")
        for path in important_paths:
            paths_text.insert(tk.END, f"• {path}\n")
        
        paths_text.config(state=tk.DISABLED)
    
    def create_services_tab(self):
        """创建服务标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="服务监控")
        
        # 状态信息
        status_frame = ttk.LabelFrame(frame, text="监控状态", padding=10)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.services_status_label = ttk.Label(status_frame, text="状态: 未启动")
        self.services_status_label.pack()
        
        # 服务列表
        services_frame = ttk.LabelFrame(frame, text="系统服务", padding=10)
        services_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建服务列表
        columns = ('服务名', '显示名', '状态', '启动类型', 'PID', '关键服务')
        self.services_tree = ttk.Treeview(services_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.services_tree.heading(col, text=col)
            self.services_tree.column(col, width=120)
        
        services_scrollbar = ttk.Scrollbar(services_frame, orient=tk.VERTICAL, command=self.services_tree.yview)
        self.services_tree.configure(yscrollcommand=services_scrollbar.set)
        
        self.services_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        services_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_filesystem_tab(self):
        """创建文件系统标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="文件系统监控")
        
        # 状态信息
        status_frame = ttk.LabelFrame(frame, text="监控状态", padding=10)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.filesystem_status_label = ttk.Label(status_frame, text="状态: 未启动")
        self.filesystem_status_label.pack()
        
        # 监控路径管理
        paths_frame = ttk.LabelFrame(frame, text="监控路径", padding=10)
        paths_frame.pack(fill=tk.BOTH, expand=True)
        
        # 路径列表
        self.paths_listbox = tk.Listbox(paths_frame, height=10)
        paths_scrollbar = ttk.Scrollbar(paths_frame, orient=tk.VERTICAL, command=self.paths_listbox.yview)
        self.paths_listbox.configure(yscrollcommand=paths_scrollbar.set)
        
        self.paths_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        paths_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 路径管理按钮
        path_buttons_frame = ttk.Frame(paths_frame)
        path_buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(path_buttons_frame, text="添加路径", command=self.add_monitor_path).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(path_buttons_frame, text="删除路径", command=self.remove_monitor_path).pack(side=tk.LEFT)
        
        # 磁盘使用情况
        disk_frame = ttk.LabelFrame(frame, text="磁盘使用情况", padding=10)
        disk_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.disk_usage_label = ttk.Label(disk_frame, text="磁盘使用情况将在这里显示")
        self.disk_usage_label.pack()
    
    def create_temp_files_tab(self):
        """创建临时文件标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="临时文件监控")
        
        # 状态信息
        status_frame = ttk.LabelFrame(frame, text="监控状态", padding=10)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.temp_status_label = ttk.Label(status_frame, text="状态: 未启动")
        self.temp_status_label.pack()
        
        # 临时目录信息
        temp_dirs_frame = ttk.LabelFrame(frame, text="临时目录", padding=10)
        temp_dirs_frame.pack(fill=tk.BOTH, expand=True)
        
        columns = ('目录名', '路径', '文件数', '总大小', '状态')
        self.temp_dirs_tree = ttk.Treeview(temp_dirs_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.temp_dirs_tree.heading(col, text=col)
            self.temp_dirs_tree.column(col, width=150)
        
        temp_dirs_scrollbar = ttk.Scrollbar(temp_dirs_frame, orient=tk.VERTICAL, command=self.temp_dirs_tree.yview)
        self.temp_dirs_tree.configure(yscrollcommand=temp_dirs_scrollbar.set)
        
        self.temp_dirs_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        temp_dirs_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_statistics_tab(self):
        """创建统计标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="统计信息")
        
        # 总体统计
        overall_frame = ttk.LabelFrame(frame, text="总体统计", padding=10)
        overall_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.overall_stats_label = ttk.Label(overall_frame, text="统计信息将在这里显示", justify=tk.LEFT)
        self.overall_stats_label.pack(anchor=tk.W)
        
        # 详细统计
        detailed_frame = ttk.LabelFrame(frame, text="详细统计", padding=10)
        detailed_frame.pack(fill=tk.BOTH, expand=True)
        
        self.detailed_stats_text = tk.Text(detailed_frame, wrap=tk.WORD)
        detailed_scrollbar = ttk.Scrollbar(detailed_frame, orient=tk.VERTICAL, command=self.detailed_stats_text.yview)
        self.detailed_stats_text.configure(yscrollcommand=detailed_scrollbar.set)
        
        self.detailed_stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        detailed_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_label = ttk.Label(self.status_bar, text="就绪")
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        self.time_label = ttk.Label(self.status_bar, text="")
        self.time_label.pack(side=tk.RIGHT, padx=10, pady=5)

    # 监控控制方法
    def toggle_registry_monitor(self):
        """切换注册表监控"""
        if self.registry_var.get():
            self.registry_monitor.start_monitoring()
            self.monitoring_status['registry'] = True
            self.status_label.config(text="注册表监控已启动")
        else:
            self.registry_monitor.stop_monitoring()
            self.monitoring_status['registry'] = False
            self.status_label.config(text="注册表监控已停止")

    def toggle_service_monitor(self):
        """切换服务监控"""
        if self.services_var.get():
            self.service_monitor.start_monitoring()
            self.monitoring_status['services'] = True
            self.status_label.config(text="服务监控已启动")
        else:
            self.service_monitor.stop_monitoring()
            self.monitoring_status['services'] = False
            self.status_label.config(text="服务监控已停止")

    def toggle_filesystem_monitor(self):
        """切换文件系统监控"""
        if self.filesystem_var.get():
            self.filesystem_monitor.start_monitoring()
            self.monitoring_status['filesystem'] = True
            self.status_label.config(text="文件系统监控已启动")
        else:
            self.filesystem_monitor.stop_monitoring()
            self.monitoring_status['filesystem'] = False
            self.status_label.config(text="文件系统监控已停止")

    def toggle_temp_monitor(self):
        """切换临时文件监控"""
        if self.temp_var.get():
            self.temp_monitor.start_monitoring()
            self.monitoring_status['temp_files'] = True
            self.status_label.config(text="临时文件监控已启动")
        else:
            self.temp_monitor.stop_monitoring()
            self.monitoring_status['temp_files'] = False
            self.status_label.config(text="临时文件监控已停止")

    def start_all_monitors(self):
        """启动所有监控"""
        self.registry_var.set(True)
        self.services_var.set(True)
        self.filesystem_var.set(True)
        self.temp_var.set(True)

        self.toggle_registry_monitor()
        self.toggle_service_monitor()
        self.toggle_filesystem_monitor()
        self.toggle_temp_monitor()

        self.status_label.config(text="所有监控已启动")

    def stop_all_monitors(self):
        """停止所有监控"""
        self.registry_var.set(False)
        self.services_var.set(False)
        self.filesystem_var.set(False)
        self.temp_var.set(False)

        self.toggle_registry_monitor()
        self.toggle_service_monitor()
        self.toggle_filesystem_monitor()
        self.toggle_temp_monitor()

        self.status_label.config(text="所有监控已停止")

    def clear_logs(self):
        """清空日志"""
        if messagebox.askyesno("确认", "确定要清空所有日志记录吗？"):
            self.logger.clear_events()
            self.refresh_events()
            self.status_label.config(text="日志已清空")

    def export_logs(self):
        """导出日志"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                events = self.logger.get_recent_events(limit=10000)
                export_data = {
                    'export_time': datetime.now().isoformat(),
                    'events': [
                        {
                            'timestamp': event[0],
                            'event_type': event[1],
                            'source': event[2],
                            'action': event[3],
                            'details': event[4],
                            'severity': event[5]
                        }
                        for event in events
                    ]
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

                self.status_label.config(text=f"日志已导出到 {filename}")

            except Exception as e:
                messagebox.showerror("错误", f"导出日志失败: {e}")

    def open_settings(self):
        """打开设置窗口"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("设置")
        settings_window.geometry("600x400")
        settings_window.transient(self.root)
        settings_window.grab_set()

        # 这里可以添加设置界面的具体实现
        ttk.Label(settings_window, text="设置功能待实现").pack(pady=20)
        ttk.Button(settings_window, text="关闭", command=settings_window.destroy).pack()

    # 数据更新方法
    def refresh_events(self):
        """刷新事件列表"""
        # 清空现有项目
        for item in self.events_tree.get_children():
            self.events_tree.delete(item)

        # 获取过滤类型
        filter_type = self.filter_var.get()
        event_type = None if filter_type == "全部" else filter_type

        # 获取最新事件
        events = self.logger.get_recent_events(limit=1000, event_type=event_type)

        # 添加到树形控件
        for event in events:
            timestamp, event_type, source, action, details, severity = event

            # 根据严重程度设置颜色标签
            tags = []
            if severity == 'ERROR':
                tags = ['error']
            elif severity == 'WARNING':
                tags = ['warning']

            self.events_tree.insert('', 0, values=(
                timestamp, event_type, source, action, details, severity
            ), tags=tags)

        # 配置标签颜色
        self.events_tree.tag_configure('error', background='#ffcccc')
        self.events_tree.tag_configure('warning', background='#fff3cd')

    def filter_events(self, event=None):
        """过滤事件"""
        self.refresh_events()

    def update_services_list(self):
        """更新服务列表"""
        # 清空现有项目
        for item in self.services_tree.get_children():
            self.services_tree.delete(item)

        # 获取服务列表
        services = self.service_monitor.get_service_list()

        for service in services:
            tags = []
            if service['is_critical']:
                tags = ['critical']
            if service['status'] == 'stopped' and service['is_critical']:
                tags = ['critical_stopped']

            self.services_tree.insert('', 'end', values=(
                service['name'],
                service['display_name'],
                service['status'],
                service['start_type'],
                service['pid'] or '',
                '是' if service['is_critical'] else '否'
            ), tags=tags)

        # 配置标签颜色
        self.services_tree.tag_configure('critical', background='#e6f3ff')
        self.services_tree.tag_configure('critical_stopped', background='#ffcccc')

    def update_filesystem_paths(self):
        """更新文件系统监控路径列表"""
        self.paths_listbox.delete(0, tk.END)

        status = self.filesystem_monitor.get_monitoring_status()
        for path in status['monitor_paths']:
            self.paths_listbox.insert(tk.END, path)

    def add_monitor_path(self):
        """添加监控路径"""
        path = filedialog.askdirectory(title="选择要监控的目录")
        if path:
            if self.filesystem_monitor.add_monitor_path(path):
                self.update_filesystem_paths()
                self.status_label.config(text=f"已添加监控路径: {path}")
            else:
                messagebox.showerror("错误", f"添加监控路径失败: {path}")

    def remove_monitor_path(self):
        """删除监控路径"""
        selection = self.paths_listbox.curselection()
        if selection:
            path = self.paths_listbox.get(selection[0])
            if self.filesystem_monitor.remove_monitor_path(path):
                self.update_filesystem_paths()
                self.status_label.config(text=f"已删除监控路径: {path}")
            else:
                messagebox.showerror("错误", f"删除监控路径失败: {path}")

    def update_disk_usage(self):
        """更新磁盘使用情况"""
        disk_usage = self.filesystem_monitor.get_disk_usage()

        usage_text = "磁盘使用情况:\n"
        for drive, usage in disk_usage.items():
            total_gb = usage['total'] / (1024**3)
            used_gb = usage['used'] / (1024**3)
            free_gb = usage['free'] / (1024**3)
            percent = usage['percent']

            usage_text += f"{drive} 总计: {total_gb:.1f}GB, "
            usage_text += f"已用: {used_gb:.1f}GB ({percent:.1f}%), "
            usage_text += f"可用: {free_gb:.1f}GB\n"

        self.disk_usage_label.config(text=usage_text)

    def update_temp_directories(self):
        """更新临时目录信息"""
        # 清空现有项目
        for item in self.temp_dirs_tree.get_children():
            self.temp_dirs_tree.delete(item)

        # 获取临时目录信息
        temp_info = self.temp_monitor.get_temp_directory_info()

        for info in temp_info:
            if info['exists']:
                if 'error' in info:
                    status = f"错误: {info['error']}"
                    file_count = "N/A"
                    total_size = "N/A"
                else:
                    status = "正常"
                    file_count = str(info.get('file_count', 0))
                    total_size_mb = info.get('total_size', 0) / (1024 * 1024)
                    total_size = f"{total_size_mb:.1f} MB"
            else:
                status = "不存在"
                file_count = "N/A"
                total_size = "N/A"

            self.temp_dirs_tree.insert('', 'end', values=(
                info['name'],
                info['path'],
                file_count,
                total_size,
                status
            ))

    def update_statistics(self):
        """更新统计信息"""
        # 获取各监控器的状态
        registry_status = self.registry_monitor.get_monitoring_status()
        service_status = self.service_monitor.get_monitoring_status()
        filesystem_status = self.filesystem_monitor.get_monitoring_status()
        temp_status = self.temp_monitor.get_monitoring_status()

        # 总体统计
        overall_text = f"""监控状态:
注册表监控: {'运行中' if registry_status['monitoring'] else '已停止'}
服务监控: {'运行中' if service_status['monitoring'] else '已停止'} ({service_status['total_services']} 个服务)
文件系统监控: {'运行中' if filesystem_status['monitoring'] else '已停止'} ({len(filesystem_status['monitor_paths'])} 个路径)
临时文件监控: {'运行中' if temp_status['monitoring'] else '已停止'} ({temp_status['temp_directories']} 个目录)"""

        self.overall_stats_label.config(text=overall_text)

        # 详细统计
        detailed_text = f"""详细统计信息:

注册表监控:
- 快照大小: {registry_status['snapshot_size']} 个键

服务监控:
- 总服务数: {service_status['total_services']}
- 运行中服务: {service_status['running_services']}
- 已停止服务: {service_status['stopped_services']}

文件系统监控:
- 监控路径数: {len(filesystem_status['monitor_paths'])}
- 活动观察者: {filesystem_status['active_observers']}
- 排除扩展名: {', '.join(filesystem_status['exclude_extensions'])}

临时文件监控:
- 临时目录数: {temp_status['temp_directories']}
- 活动观察者: {temp_status['active_observers']}
"""

        self.detailed_stats_text.delete(1.0, tk.END)
        self.detailed_stats_text.insert(1.0, detailed_text)

    def update_status_labels(self):
        """更新状态标签"""
        # 注册表状态
        registry_status = self.registry_monitor.get_monitoring_status()
        registry_text = f"状态: {'运行中' if registry_status['monitoring'] else '已停止'}"
        if registry_status['monitoring']:
            registry_text += f" (快照大小: {registry_status['snapshot_size']})"
        self.registry_status_label.config(text=registry_text)

        # 服务状态
        service_status = self.service_monitor.get_monitoring_status()
        services_text = f"状态: {'运行中' if service_status['monitoring'] else '已停止'}"
        if service_status['monitoring']:
            services_text += f" (总计: {service_status['total_services']}, 运行: {service_status['running_services']})"
        self.services_status_label.config(text=services_text)

        # 文件系统状态
        filesystem_status = self.filesystem_monitor.get_monitoring_status()
        filesystem_text = f"状态: {'运行中' if filesystem_status['monitoring'] else '已停止'}"
        if filesystem_status['monitoring']:
            filesystem_text += f" (路径: {len(filesystem_status['monitor_paths'])}, 观察者: {filesystem_status['active_observers']})"
        self.filesystem_status_label.config(text=filesystem_text)

        # 临时文件状态
        temp_status = self.temp_monitor.get_monitoring_status()
        temp_text = f"状态: {'运行中' if temp_status['monitoring'] else '已停止'}"
        if temp_status['monitoring']:
            temp_text += f" (目录: {temp_status['temp_directories']}, 观察者: {temp_status['active_observers']})"
        self.temp_status_label.config(text=temp_text)

    def update_display(self):
        """定期更新显示"""
        try:
            # 更新时间
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.time_label.config(text=current_time)

            # 更新各种状态和数据
            self.update_status_labels()

            # 根据当前标签页更新相应数据
            current_tab = self.notebook.select()
            tab_text = self.notebook.tab(current_tab, "text")

            if tab_text == "实时监控":
                self.refresh_events()
            elif tab_text == "服务监控":
                self.update_services_list()
            elif tab_text == "文件系统监控":
                self.update_filesystem_paths()
                self.update_disk_usage()
            elif tab_text == "临时文件监控":
                self.update_temp_directories()
            elif tab_text == "统计信息":
                self.update_statistics()

        except Exception as e:
            print(f"更新显示时出错: {e}")

        # 每秒更新一次
        self.root.after(1000, self.update_display)

    def run(self):
        """运行GUI"""
        try:
            self.root.mainloop()
        finally:
            # 确保在退出时停止所有监控
            self.stop_all_monitors()

if __name__ == "__main__":
    app = MonitorGUI()
    app.run()
