#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电脑监控软件安装脚本
"""

import sys
import subprocess
import os
import platform

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True

def check_windows():
    """检查是否为Windows系统"""
    if platform.system() != 'Windows':
        print("警告: 此软件主要为Windows系统设计")
        response = input("是否继续安装? (y/N): ")
        return response.lower() in ['y', 'yes']
    return True

def install_package(package):
    """安装Python包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ {package} 安装失败")
        return False

def install_requirements():
    """安装依赖包"""
    requirements = [
        "psutil==5.9.5",
        "watchdog==3.0.0",
        "pywin32==306"
    ]
    
    print("正在安装依赖包...")
    failed_packages = []
    
    for package in requirements:
        if not install_package(package):
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n以下包安装失败: {', '.join(failed_packages)}")
        print("请手动安装这些包或检查网络连接")
        return False
    
    print("\n✓ 所有依赖包安装成功")
    return True

def create_desktop_shortcut():
    """创建桌面快捷方式"""
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "电脑监控软件.lnk")
        target = os.path.join(os.getcwd(), "main.py")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print(f"✓ 桌面快捷方式已创建: {path}")
        return True
        
    except ImportError:
        print("无法创建桌面快捷方式 (需要winshell包)")
        return False
    except Exception as e:
        print(f"创建桌面快捷方式失败: {e}")
        return False

def create_start_menu_shortcut():
    """创建开始菜单快捷方式"""
    try:
        import winshell
        from win32com.client import Dispatch
        
        start_menu = winshell.start_menu()
        programs_folder = os.path.join(start_menu, "Programs")
        app_folder = os.path.join(programs_folder, "电脑监控软件")
        
        if not os.path.exists(app_folder):
            os.makedirs(app_folder)
        
        path = os.path.join(app_folder, "电脑监控软件.lnk")
        target = os.path.join(os.getcwd(), "main.py")
        wDir = os.getcwd()
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.save()
        
        print(f"✓ 开始菜单快捷方式已创建: {path}")
        return True
        
    except Exception as e:
        print(f"创建开始菜单快捷方式失败: {e}")
        return False

def test_installation():
    """测试安装"""
    print("\n正在测试安装...")
    
    try:
        # 测试导入主要模块
        from logger import MonitorLogger
        from registry_monitor import RegistryMonitor
        from service_monitor import ServiceMonitor
        from filesystem_monitor import FileSystemMonitor
        from temp_monitor import TempFileMonitor
        
        print("✓ 所有模块导入成功")
        
        # 测试创建实例
        logger = MonitorLogger()
        print("✓ 日志系统初始化成功")
        
        registry_monitor = RegistryMonitor(logger)
        service_monitor = ServiceMonitor(logger)
        filesystem_monitor = FileSystemMonitor(logger)
        temp_monitor = TempFileMonitor(logger)
        
        print("✓ 所有监控模块初始化成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def main():
    """主安装函数"""
    print("电脑监控软件安装程序")
    print("=" * 40)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查操作系统
    if not check_windows():
        sys.exit(1)
    
    # 安装依赖包
    if not install_requirements():
        print("\n安装失败，请检查错误信息")
        sys.exit(1)
    
    # 测试安装
    if not test_installation():
        print("\n安装测试失败，请检查错误信息")
        sys.exit(1)
    
    # 创建快捷方式
    print("\n正在创建快捷方式...")
    create_desktop_shortcut()
    create_start_menu_shortcut()
    
    print("\n" + "=" * 40)
    print("✓ 安装完成!")
    print("\n使用方法:")
    print("1. 双击桌面快捷方式启动GUI界面")
    print("2. 或在命令行运行: python main.py")
    print("3. 控制台模式: python main.py --mode console")
    print("4. 查看帮助: python main.py --help")
    print("\n配置文件: config.ini")
    print("日志文件: monitor.log")
    print("数据库文件: monitor.db")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
