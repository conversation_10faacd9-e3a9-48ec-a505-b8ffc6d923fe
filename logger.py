import logging
import logging.handlers
import sqlite3
import threading
from datetime import datetime
import configparser
import os

class MonitorLogger:
    def __init__(self, config_file='config.ini'):
        self.config = configparser.ConfigParser()
        self.config.read(config_file, encoding='utf-8')
        
        # 设置文件日志
        self.setup_file_logger()
        
        # 设置数据库日志
        self.setup_database()
        
        self.lock = threading.Lock()
    
    def setup_file_logger(self):
        """设置文件日志记录器"""
        log_file = self.config.get('LOGGING', 'log_file', fallback='monitor.log')
        log_level = self.config.get('LOGGING', 'log_level', fallback='INFO')
        max_size = self.config.getint('LOGGING', 'max_log_size', fallback=10485760)
        backup_count = self.config.getint('LOGGING', 'backup_count', fallback=5)
        
        self.logger = logging.getLogger('MonitorLogger')
        self.logger.setLevel(getattr(logging, log_level))
        
        # 创建轮转文件处理器
        handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=max_size, backupCount=backup_count, encoding='utf-8'
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def setup_database(self):
        """设置数据库日志"""
        db_file = self.config.get('DATABASE', 'db_file', fallback='monitor.db')
        self.db_file = db_file
        
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 创建监控事件表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS monitor_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                event_type TEXT NOT NULL,
                source TEXT NOT NULL,
                action TEXT NOT NULL,
                details TEXT,
                severity TEXT DEFAULT 'INFO'
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def log_event(self, event_type, source, action, details='', severity='INFO'):
        """记录监控事件"""
        with self.lock:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 记录到文件
            message = f"[{event_type}] {source} - {action}"
            if details:
                message += f" - {details}"
            
            if severity == 'ERROR':
                self.logger.error(message)
            elif severity == 'WARNING':
                self.logger.warning(message)
            else:
                self.logger.info(message)
            
            # 记录到数据库
            try:
                conn = sqlite3.connect(self.db_file)
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO monitor_events 
                    (timestamp, event_type, source, action, details, severity)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (timestamp, event_type, source, action, details, severity))
                
                conn.commit()
                
                # 清理旧记录
                max_records = self.config.getint('DATABASE', 'max_records', fallback=10000)
                cursor.execute('''
                    DELETE FROM monitor_events 
                    WHERE id NOT IN (
                        SELECT id FROM monitor_events 
                        ORDER BY id DESC LIMIT ?
                    )
                ''', (max_records,))
                
                conn.commit()
                conn.close()
                
            except Exception as e:
                self.logger.error(f"数据库记录失败: {e}")
    
    def get_recent_events(self, limit=100, event_type=None):
        """获取最近的事件记录"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            if event_type:
                cursor.execute('''
                    SELECT timestamp, event_type, source, action, details, severity
                    FROM monitor_events 
                    WHERE event_type = ?
                    ORDER BY id DESC LIMIT ?
                ''', (event_type, limit))
            else:
                cursor.execute('''
                    SELECT timestamp, event_type, source, action, details, severity
                    FROM monitor_events 
                    ORDER BY id DESC LIMIT ?
                ''', (limit,))
            
            events = cursor.fetchall()
            conn.close()
            return events
            
        except Exception as e:
            self.logger.error(f"获取事件记录失败: {e}")
            return []
    
    def clear_events(self):
        """清空事件记录"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            cursor.execute('DELETE FROM monitor_events')
            conn.commit()
            conn.close()
            self.logger.info("事件记录已清空")
        except Exception as e:
            self.logger.error(f"清空事件记录失败: {e}")
