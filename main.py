#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电脑监控软件主程序
功能：监控注册表、服务、文件系统、临时文件变化
作者：AI Assistant
版本：1.0
"""

import sys
import os
import argparse
import signal
import time
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from logger import MonitorLogger
from registry_monitor import RegistryMonitor
from service_monitor import ServiceMonitor
from filesystem_monitor import FileSystemMonitor
from temp_monitor import TempFileMonitor

class MonitorApp:
    def __init__(self):
        self.logger = MonitorLogger()
        self.registry_monitor = RegistryMonitor(self.logger)
        self.service_monitor = ServiceMonitor(self.logger)
        self.filesystem_monitor = FileSystemMonitor(self.logger)
        self.temp_monitor = TempFileMonitor(self.logger)
        
        self.running = False
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在停止监控...")
        self.stop_monitoring()
        sys.exit(0)
    
    def start_monitoring(self, enable_registry=True, enable_services=True, 
                        enable_filesystem=True, enable_temp=True):
        """启动监控"""
        self.running = True
        
        print("正在启动电脑监控软件...")
        self.logger.log_event('SYSTEM', 'Application', 'Started', '电脑监控软件已启动')
        
        # 启动各个监控模块
        if enable_registry:
            try:
                self.registry_monitor.start_monitoring()
                print("✓ 注册表监控已启动")
            except Exception as e:
                print(f"✗ 注册表监控启动失败: {e}")
                self.logger.log_event('SYSTEM', 'Registry Monitor', 'Start Failed', str(e), 'ERROR')
        
        if enable_services:
            try:
                self.service_monitor.start_monitoring()
                print("✓ 服务监控已启动")
            except Exception as e:
                print(f"✗ 服务监控启动失败: {e}")
                self.logger.log_event('SYSTEM', 'Service Monitor', 'Start Failed', str(e), 'ERROR')
        
        if enable_filesystem:
            try:
                self.filesystem_monitor.start_monitoring()
                print("✓ 文件系统监控已启动")
            except Exception as e:
                print(f"✗ 文件系统监控启动失败: {e}")
                self.logger.log_event('SYSTEM', 'Filesystem Monitor', 'Start Failed', str(e), 'ERROR')
        
        if enable_temp:
            try:
                self.temp_monitor.start_monitoring()
                print("✓ 临时文件监控已启动")
            except Exception as e:
                print(f"✗ 临时文件监控启动失败: {e}")
                self.logger.log_event('SYSTEM', 'Temp Monitor', 'Start Failed', str(e), 'ERROR')
        
        print("\n监控已启动，按 Ctrl+C 停止监控")
        print("=" * 50)
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.running:
            return
        
        self.running = False
        
        print("\n正在停止监控...")
        
        try:
            self.registry_monitor.stop_monitoring()
            print("✓ 注册表监控已停止")
        except Exception as e:
            print(f"✗ 停止注册表监控失败: {e}")
        
        try:
            self.service_monitor.stop_monitoring()
            print("✓ 服务监控已停止")
        except Exception as e:
            print(f"✗ 停止服务监控失败: {e}")
        
        try:
            self.filesystem_monitor.stop_monitoring()
            print("✓ 文件系统监控已停止")
        except Exception as e:
            print(f"✗ 停止文件系统监控失败: {e}")
        
        try:
            self.temp_monitor.stop_monitoring()
            print("✓ 临时文件监控已停止")
        except Exception as e:
            print(f"✗ 停止临时文件监控失败: {e}")
        
        self.logger.log_event('SYSTEM', 'Application', 'Stopped', '电脑监控软件已停止')
        print("监控已停止")
    
    def run_console(self, enable_registry=True, enable_services=True, 
                   enable_filesystem=True, enable_temp=True):
        """运行控制台模式"""
        self.start_monitoring(enable_registry, enable_services, enable_filesystem, enable_temp)
        
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_monitoring()
    
    def run_gui(self):
        """运行GUI模式"""
        try:
            from gui import MonitorGUI
            app = MonitorGUI()
            app.run()
        except ImportError as e:
            print(f"无法启动GUI模式: {e}")
            print("请确保已安装tkinter库")
            sys.exit(1)
        except Exception as e:
            print(f"GUI模式运行失败: {e}")
            sys.exit(1)
    
    def show_status(self):
        """显示监控状态"""
        print("电脑监控软件状态:")
        print("=" * 30)
        
        # 注册表监控状态
        registry_status = self.registry_monitor.get_monitoring_status()
        print(f"注册表监控: {'运行中' if registry_status['monitoring'] else '已停止'}")
        if registry_status['monitoring']:
            print(f"  快照大小: {registry_status['snapshot_size']} 个键")
        
        # 服务监控状态
        service_status = self.service_monitor.get_monitoring_status()
        print(f"服务监控: {'运行中' if service_status['monitoring'] else '已停止'}")
        if service_status['monitoring']:
            print(f"  总服务数: {service_status['total_services']}")
            print(f"  运行中: {service_status['running_services']}")
            print(f"  已停止: {service_status['stopped_services']}")
        
        # 文件系统监控状态
        filesystem_status = self.filesystem_monitor.get_monitoring_status()
        print(f"文件系统监控: {'运行中' if filesystem_status['monitoring'] else '已停止'}")
        if filesystem_status['monitoring']:
            print(f"  监控路径: {len(filesystem_status['monitor_paths'])}")
            print(f"  活动观察者: {filesystem_status['active_observers']}")
        
        # 临时文件监控状态
        temp_status = self.temp_monitor.get_monitoring_status()
        print(f"临时文件监控: {'运行中' if temp_status['monitoring'] else '已停止'}")
        if temp_status['monitoring']:
            print(f"  临时目录数: {temp_status['temp_directories']}")
            print(f"  活动观察者: {temp_status['active_observers']}")
        
        print("=" * 30)
    
    def show_recent_events(self, limit=20):
        """显示最近的事件"""
        events = self.logger.get_recent_events(limit=limit)
        
        print(f"最近 {len(events)} 个事件:")
        print("=" * 80)
        print(f"{'时间':<20} {'类型':<12} {'来源':<20} {'操作':<15} {'严重程度':<8}")
        print("-" * 80)
        
        for event in events:
            timestamp, event_type, source, action, details, severity = event
            # 截断长字符串
            source = source[:18] + ".." if len(source) > 20 else source
            action = action[:13] + ".." if len(action) > 15 else action
            
            print(f"{timestamp:<20} {event_type:<12} {source:<20} {action:<15} {severity:<8}")
        
        print("=" * 80)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='电脑监控软件')
    parser.add_argument('--mode', choices=['gui', 'console'], default='gui',
                       help='运行模式 (默认: gui)')
    parser.add_argument('--no-registry', action='store_true',
                       help='禁用注册表监控')
    parser.add_argument('--no-services', action='store_true',
                       help='禁用服务监控')
    parser.add_argument('--no-filesystem', action='store_true',
                       help='禁用文件系统监控')
    parser.add_argument('--no-temp', action='store_true',
                       help='禁用临时文件监控')
    parser.add_argument('--status', action='store_true',
                       help='显示监控状态')
    parser.add_argument('--events', type=int, metavar='N',
                       help='显示最近N个事件')
    
    args = parser.parse_args()
    
    # 检查是否为Windows系统
    if os.name != 'nt':
        print("警告: 此软件主要为Windows系统设计，在其他系统上可能无法正常工作")
    
    app = MonitorApp()
    
    # 处理状态查询
    if args.status:
        app.show_status()
        return
    
    # 处理事件查询
    if args.events:
        app.show_recent_events(args.events)
        return
    
    # 确定启用的监控模块
    enable_registry = not args.no_registry
    enable_services = not args.no_services
    enable_filesystem = not args.no_filesystem
    enable_temp = not args.no_temp
    
    # 运行应用
    if args.mode == 'gui':
        app.run_gui()
    else:
        app.run_console(enable_registry, enable_services, enable_filesystem, enable_temp)

if __name__ == "__main__":
    main()
