import winreg
import threading
import time
import hashlib
import json
from logger import MonitorLogger

class RegistryMonitor:
    def __init__(self, logger=None):
        self.logger = logger or MonitorLogger()
        self.monitoring = False
        self.monitor_thread = None
        self.registry_snapshot = {}
        
        # 要监控的注册表根键
        self.root_keys = {
            'HKEY_CURRENT_USER': winreg.HKEY_CURRENT_USER,
            'HKEY_LOCAL_MACHINE': winreg.HKEY_LOCAL_MACHINE,
            'HKEY_CLASSES_ROOT': winreg.HKEY_CLASSES_ROOT,
            'HKEY_USERS': winreg.HKEY_USERS,
            'HKEY_CURRENT_CONFIG': winreg.HKEY_CURRENT_CONFIG
        }
        
        # 重要的监控路径
        self.important_paths = [
            (winreg.HKEY_CURRENT_USER, "Software\\Microsoft\\Windows\\CurrentVersion\\Run"),
            (winreg.HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run"),
            (winreg.HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce"),
            (winreg.HKEY_LOCAL_MACHINE, "SYSTEM\\CurrentControlSet\\Services"),
            (winreg.HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Winlogon"),
            (winreg.HKEY_CURRENT_USER, "Software\\Microsoft\\Windows\\CurrentVersion\\Policies"),
            (winreg.HKEY_LOCAL_MACHINE, "SOFTWARE\\Policies"),
        ]
    
    def start_monitoring(self):
        """开始监控注册表"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.logger.log_event('REGISTRY', 'Monitor', 'Started', '注册表监控已启动')
        
        # 创建初始快照
        self.create_snapshot()
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控注册表"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.log_event('REGISTRY', 'Monitor', 'Stopped', '注册表监控已停止')
    
    def create_snapshot(self):
        """创建注册表快照"""
        self.registry_snapshot = {}
        
        for root_name, root_key in self.root_keys.items():
            try:
                self.registry_snapshot[root_name] = self._scan_key(root_key, "", max_depth=3)
            except Exception as e:
                self.logger.log_event('REGISTRY', 'Snapshot', 'Error', 
                                    f'扫描 {root_name} 失败: {e}', 'ERROR')
    
    def _scan_key(self, root_key, subkey_path, max_depth=3, current_depth=0):
        """递归扫描注册表键"""
        if current_depth >= max_depth:
            return {}
        
        key_data = {}
        
        try:
            with winreg.OpenKey(root_key, subkey_path) as key:
                # 获取值
                values = {}
                try:
                    i = 0
                    while True:
                        try:
                            name, value, reg_type = winreg.EnumValue(key, i)
                            values[name] = {
                                'value': str(value) if value is not None else '',
                                'type': reg_type
                            }
                            i += 1
                        except WindowsError:
                            break
                except Exception:
                    pass
                
                key_data['values'] = values
                
                # 获取子键
                subkeys = {}
                try:
                    i = 0
                    while True:
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            if current_depth < max_depth - 1:
                                subkey_full_path = f"{subkey_path}\\{subkey_name}" if subkey_path else subkey_name
                                subkeys[subkey_name] = self._scan_key(root_key, subkey_full_path, 
                                                                    max_depth, current_depth + 1)
                            i += 1
                        except WindowsError:
                            break
                except Exception:
                    pass
                
                key_data['subkeys'] = subkeys
                
        except Exception as e:
            # 某些键可能无法访问，这是正常的
            pass
        
        return key_data
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # 检查重要路径的变化
                self._check_important_paths()
                
                # 每30秒进行一次全面检查
                time.sleep(30)
                
            except Exception as e:
                self.logger.log_event('REGISTRY', 'Monitor', 'Error', 
                                    f'监控循环错误: {e}', 'ERROR')
                time.sleep(5)
    
    def _check_important_paths(self):
        """检查重要路径的变化"""
        for root_key, path in self.important_paths:
            try:
                current_data = self._scan_key(root_key, path, max_depth=2)
                
                # 生成路径标识
                path_id = f"{root_key}\\{path}"
                
                if path_id in self.registry_snapshot:
                    old_data = self.registry_snapshot[path_id]
                    changes = self._compare_key_data(old_data, current_data, path)
                    
                    for change in changes:
                        self.logger.log_event('REGISTRY', path, change['action'], 
                                            change['details'], change['severity'])
                
                # 更新快照
                self.registry_snapshot[path_id] = current_data
                
            except Exception as e:
                self.logger.log_event('REGISTRY', path, 'Error', 
                                    f'检查路径失败: {e}', 'ERROR')
    
    def _compare_key_data(self, old_data, new_data, path):
        """比较注册表数据变化"""
        changes = []
        
        # 比较值的变化
        old_values = old_data.get('values', {})
        new_values = new_data.get('values', {})
        
        # 检查新增的值
        for name, value_data in new_values.items():
            if name not in old_values:
                changes.append({
                    'action': 'Value Added',
                    'details': f'新增值: {name} = {value_data["value"]}',
                    'severity': 'WARNING' if self._is_suspicious_value(name, value_data) else 'INFO'
                })
        
        # 检查修改的值
        for name, value_data in new_values.items():
            if name in old_values and old_values[name] != value_data:
                changes.append({
                    'action': 'Value Modified',
                    'details': f'修改值: {name} = {value_data["value"]} (原值: {old_values[name]["value"]})',
                    'severity': 'WARNING' if self._is_suspicious_value(name, value_data) else 'INFO'
                })
        
        # 检查删除的值
        for name in old_values:
            if name not in new_values:
                changes.append({
                    'action': 'Value Deleted',
                    'details': f'删除值: {name}',
                    'severity': 'INFO'
                })
        
        # 比较子键的变化
        old_subkeys = old_data.get('subkeys', {})
        new_subkeys = new_data.get('subkeys', {})
        
        # 检查新增的子键
        for subkey_name in new_subkeys:
            if subkey_name not in old_subkeys:
                changes.append({
                    'action': 'Subkey Added',
                    'details': f'新增子键: {subkey_name}',
                    'severity': 'INFO'
                })
        
        # 检查删除的子键
        for subkey_name in old_subkeys:
            if subkey_name not in new_subkeys:
                changes.append({
                    'action': 'Subkey Deleted',
                    'details': f'删除子键: {subkey_name}',
                    'severity': 'INFO'
                })
        
        return changes
    
    def _is_suspicious_value(self, name, value_data):
        """检查是否为可疑的注册表值"""
        suspicious_patterns = [
            'cmd.exe', 'powershell', 'wscript', 'cscript',
            'regsvr32', 'rundll32', 'mshta', 'bitsadmin'
        ]
        
        value_str = str(value_data.get('value', '')).lower()
        return any(pattern in value_str for pattern in suspicious_patterns)
    
    def get_monitoring_status(self):
        """获取监控状态"""
        return {
            'monitoring': self.monitoring,
            'snapshot_size': len(self.registry_snapshot)
        }
