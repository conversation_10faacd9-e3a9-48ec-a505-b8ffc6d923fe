import psutil
import threading
import time
from logger import MonitorLogger

class ServiceMonitor:
    def __init__(self, logger=None):
        self.logger = logger or MonitorLogger()
        self.monitoring = False
        self.monitor_thread = None
        self.services_snapshot = {}
        self.check_interval = 10  # 检查间隔（秒）
        
        # 重要的系统服务
        self.critical_services = {
            'Windows Defender Antivirus Service',
            'Windows Security Service',
            'Windows Firewall',
            'Windows Update',
            'Task Scheduler',
            'Remote Desktop Services',
            'Server',
            'Workstation',
            'DNS Client',
            'DHCP Client',
            'Windows Time',
            'Plug and Play',
            'Print Spooler',
            'Themes',
            'Audio Service',
            'Windows Audio Endpoint Builder'
        }
    
    def start_monitoring(self):
        """开始监控服务"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.logger.log_event('SERVICE', 'Monitor', 'Started', '服务监控已启动')
        
        # 创建初始快照
        self.create_snapshot()
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控服务"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.log_event('SERVICE', 'Monitor', 'Stopped', '服务监控已停止')
    
    def create_snapshot(self):
        """创建服务状态快照"""
        self.services_snapshot = {}
        
        try:
            # 获取所有Windows服务
            for service in psutil.win_service_iter():
                try:
                    service_info = service.as_dict()
                    service_name = service_info['name']
                    
                    self.services_snapshot[service_name] = {
                        'display_name': service_info.get('display_name', ''),
                        'status': service_info.get('status', ''),
                        'start_type': service_info.get('start_type', ''),
                        'pid': service_info.get('pid', None),
                        'binpath': service_info.get('binpath', ''),
                        'username': service_info.get('username', ''),
                        'description': service_info.get('description', '')
                    }
                    
                except Exception as e:
                    self.logger.log_event('SERVICE', 'Snapshot', 'Error', 
                                        f'获取服务 {service_name} 信息失败: {e}', 'ERROR')
                    
        except Exception as e:
            self.logger.log_event('SERVICE', 'Snapshot', 'Error', 
                                f'创建服务快照失败: {e}', 'ERROR')
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                self._check_service_changes()
                time.sleep(self.check_interval)
                
            except Exception as e:
                self.logger.log_event('SERVICE', 'Monitor', 'Error', 
                                    f'监控循环错误: {e}', 'ERROR')
                time.sleep(5)
    
    def _check_service_changes(self):
        """检查服务变化"""
        current_services = {}
        
        try:
            # 获取当前服务状态
            for service in psutil.win_service_iter():
                try:
                    service_info = service.as_dict()
                    service_name = service_info['name']
                    
                    current_services[service_name] = {
                        'display_name': service_info.get('display_name', ''),
                        'status': service_info.get('status', ''),
                        'start_type': service_info.get('start_type', ''),
                        'pid': service_info.get('pid', None),
                        'binpath': service_info.get('binpath', ''),
                        'username': service_info.get('username', ''),
                        'description': service_info.get('description', '')
                    }
                    
                except Exception:
                    continue
            
            # 比较变化
            self._compare_services(self.services_snapshot, current_services)
            
            # 更新快照
            self.services_snapshot = current_services
            
        except Exception as e:
            self.logger.log_event('SERVICE', 'Monitor', 'Error', 
                                f'检查服务变化失败: {e}', 'ERROR')
    
    def _compare_services(self, old_services, new_services):
        """比较服务变化"""
        
        # 检查新增的服务
        for service_name, service_info in new_services.items():
            if service_name not in old_services:
                severity = 'WARNING' if self._is_suspicious_service(service_info) else 'INFO'
                self.logger.log_event('SERVICE', service_name, 'Service Added', 
                                    f'新增服务: {service_info["display_name"]} - {service_info["binpath"]}',
                                    severity)
        
        # 检查删除的服务
        for service_name, service_info in old_services.items():
            if service_name not in new_services:
                severity = 'WARNING' if service_name in self.critical_services else 'INFO'
                self.logger.log_event('SERVICE', service_name, 'Service Removed', 
                                    f'删除服务: {service_info["display_name"]}',
                                    severity)
        
        # 检查服务状态变化
        for service_name, new_info in new_services.items():
            if service_name in old_services:
                old_info = old_services[service_name]
                
                # 检查状态变化
                if old_info['status'] != new_info['status']:
                    severity = self._get_status_change_severity(service_name, old_info['status'], new_info['status'])
                    self.logger.log_event('SERVICE', service_name, 'Status Changed', 
                                        f'状态变化: {old_info["status"]} -> {new_info["status"]}',
                                        severity)
                
                # 检查启动类型变化
                if old_info['start_type'] != new_info['start_type']:
                    self.logger.log_event('SERVICE', service_name, 'Start Type Changed', 
                                        f'启动类型变化: {old_info["start_type"]} -> {new_info["start_type"]}',
                                        'WARNING')
                
                # 检查可执行文件路径变化
                if old_info['binpath'] != new_info['binpath']:
                    self.logger.log_event('SERVICE', service_name, 'Binary Path Changed', 
                                        f'可执行文件路径变化: {old_info["binpath"]} -> {new_info["binpath"]}',
                                        'WARNING')
                
                # 检查运行用户变化
                if old_info['username'] != new_info['username']:
                    self.logger.log_event('SERVICE', service_name, 'Username Changed', 
                                        f'运行用户变化: {old_info["username"]} -> {new_info["username"]}',
                                        'WARNING')
    
    def _is_suspicious_service(self, service_info):
        """检查是否为可疑服务"""
        suspicious_patterns = [
            'temp', 'tmp', 'cache', 'update', 'download',
            'install', 'setup', 'config', 'system32',
            'appdata', 'programdata'
        ]
        
        binpath = service_info.get('binpath', '').lower()
        display_name = service_info.get('display_name', '').lower()
        
        # 检查可执行文件路径是否可疑
        for pattern in suspicious_patterns:
            if pattern in binpath or pattern in display_name:
                return True
        
        # 检查是否在临时目录
        temp_paths = ['\\temp\\', '\\tmp\\', '\\appdata\\local\\temp\\']
        for temp_path in temp_paths:
            if temp_path in binpath:
                return True
        
        return False
    
    def _get_status_change_severity(self, service_name, old_status, new_status):
        """获取状态变化的严重程度"""
        # 关键服务停止是高优先级
        if service_name in self.critical_services and new_status == 'stopped':
            return 'ERROR'
        
        # 服务启动通常是正常的
        if new_status == 'running':
            return 'INFO'
        
        # 服务停止可能需要关注
        if new_status == 'stopped':
            return 'WARNING'
        
        return 'INFO'
    
    def get_service_list(self, status_filter=None):
        """获取服务列表"""
        services = []
        
        for name, info in self.services_snapshot.items():
            if status_filter is None or info['status'] == status_filter:
                services.append({
                    'name': name,
                    'display_name': info['display_name'],
                    'status': info['status'],
                    'start_type': info['start_type'],
                    'pid': info['pid'],
                    'is_critical': name in self.critical_services
                })
        
        return sorted(services, key=lambda x: x['display_name'])
    
    def get_monitoring_status(self):
        """获取监控状态"""
        running_count = sum(1 for info in self.services_snapshot.values() 
                          if info['status'] == 'running')
        stopped_count = sum(1 for info in self.services_snapshot.values() 
                          if info['status'] == 'stopped')
        
        return {
            'monitoring': self.monitoring,
            'total_services': len(self.services_snapshot),
            'running_services': running_count,
            'stopped_services': stopped_count
        }
