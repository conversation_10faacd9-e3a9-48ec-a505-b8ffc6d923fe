@echo off
chcp 65001 >nul
title 电脑监控软件 - 控制台模式

echo ========================================
echo      电脑监控软件 v1.0 - 控制台模式
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查是否为管理员权限
net session >nul 2>&1
if errorlevel 1 (
    echo 警告: 建议以管理员身份运行以获得完整监控权限
    echo.
)

REM 启动控制台模式
echo 正在启动控制台模式...
echo 按 Ctrl+C 停止监控
echo.
python main.py --mode console

if errorlevel 1 (
    echo.
    echo 程序运行出错，请检查错误信息
    pause
)
