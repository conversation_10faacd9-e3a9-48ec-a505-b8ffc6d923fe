import os
import tempfile
import threading
import time
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from logger import MonitorLogger

class TempFileEventHandler(FileSystemEventHandler):
    def __init__(self, logger, temp_path):
        super().__init__()
        self.logger = logger
        self.temp_path = temp_path
    
    def on_created(self, event):
        if not event.is_directory:
            file_size = self._get_file_size(event.src_path)
            severity = self._get_temp_file_severity(event.src_path)
            self.logger.log_event('TEMP_FILES', f'Created in {self.temp_path}', 
                                os.path.basename(event.src_path), 
                                f'文件大小: {file_size} bytes, 完整路径: {event.src_path}', 
                                severity)
    
    def on_deleted(self, event):
        if not event.is_directory:
            severity = self._get_temp_file_severity(event.src_path)
            self.logger.log_event('TEMP_FILES', f'Deleted from {self.temp_path}', 
                                os.path.basename(event.src_path), 
                                f'完整路径: {event.src_path}', 
                                severity)
    
    def on_modified(self, event):
        if not event.is_directory:
            file_size = self._get_file_size(event.src_path)
            severity = self._get_temp_file_severity(event.src_path)
            self.logger.log_event('TEMP_FILES', f'Modified in {self.temp_path}', 
                                os.path.basename(event.src_path), 
                                f'文件大小: {file_size} bytes, 完整路径: {event.src_path}', 
                                severity)
    
    def _get_file_size(self, path):
        """获取文件大小"""
        try:
            return os.path.getsize(path)
        except:
            return 0
    
    def _get_temp_file_severity(self, path):
        """根据临时文件类型确定严重程度"""
        path_lower = path.lower()
        filename = os.path.basename(path_lower)
        
        # 可执行文件在临时目录中是高风险的
        executable_extensions = ['.exe', '.bat', '.cmd', '.com', '.scr', '.pif', '.vbs', '.js', '.jar', '.msi']
        _, ext = os.path.splitext(path_lower)
        if ext in executable_extensions:
            return 'ERROR'
        
        # 脚本文件
        script_extensions = ['.ps1', '.py', '.pl', '.sh']
        if ext in script_extensions:
            return 'WARNING'
        
        # 压缩文件可能包含恶意软件
        archive_extensions = ['.zip', '.rar', '.7z', '.tar', '.gz']
        if ext in archive_extensions:
            return 'WARNING'
        
        # 大文件可能是下载的软件
        try:
            file_size = os.path.getsize(path)
            if file_size > 10 * 1024 * 1024:  # 大于10MB
                return 'WARNING'
        except:
            pass
        
        # 可疑的文件名模式
        suspicious_patterns = ['update', 'install', 'setup', 'download', 'cache', 'temp']
        for pattern in suspicious_patterns:
            if pattern in filename:
                return 'INFO'
        
        return 'INFO'

class TempFileMonitor:
    def __init__(self, logger=None):
        self.logger = logger or MonitorLogger()
        self.monitoring = False
        self.observers = []
        
        # 获取系统临时目录
        self.temp_directories = self._get_temp_directories()
        
        # 统计信息
        self.stats = {
            'temp_files_created': 0,
            'temp_files_deleted': 0,
            'temp_files_modified': 0,
            'suspicious_files': 0
        }
        
        # 定期清理线程
        self.cleanup_thread = None
        self.cleanup_interval = 3600  # 1小时
    
    def _get_temp_directories(self):
        """获取所有临时目录"""
        temp_dirs = []
        
        # 系统临时目录
        system_temp = tempfile.gettempdir()
        temp_dirs.append(('System Temp', system_temp))
        
        # Windows特定的临时目录
        if os.name == 'nt':
            # 用户临时目录
            user_temp = os.environ.get('TEMP', '')
            if user_temp and user_temp != system_temp:
                temp_dirs.append(('User Temp', user_temp))
            
            # 系统临时目录
            windows_temp = os.environ.get('TMP', '')
            if windows_temp and windows_temp not in [system_temp, user_temp]:
                temp_dirs.append(('Windows Temp', windows_temp))
            
            # Internet Explorer临时文件
            ie_temp = os.path.join(os.environ.get('USERPROFILE', ''), 
                                 'AppData', 'Local', 'Microsoft', 'Windows', 'INetCache')
            if os.path.exists(ie_temp):
                temp_dirs.append(('IE Cache', ie_temp))
            
            # Chrome临时文件
            chrome_temp = os.path.join(os.environ.get('USERPROFILE', ''), 
                                     'AppData', 'Local', 'Google', 'Chrome', 'User Data', 'Default', 'Cache')
            if os.path.exists(chrome_temp):
                temp_dirs.append(('Chrome Cache', chrome_temp))
            
            # Firefox临时文件
            firefox_temp = os.path.join(os.environ.get('USERPROFILE', ''), 
                                      'AppData', 'Local', 'Mozilla', 'Firefox', 'Profiles')
            if os.path.exists(firefox_temp):
                temp_dirs.append(('Firefox Cache', firefox_temp))
            
            # 回收站
            recycle_bin = 'C:\\$Recycle.Bin'
            if os.path.exists(recycle_bin):
                temp_dirs.append(('Recycle Bin', recycle_bin))
        
        return temp_dirs
    
    def start_monitoring(self):
        """开始监控临时文件"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.logger.log_event('TEMP_FILES', 'Monitor', 'Started', 
                            f'临时文件监控已启动，监控 {len(self.temp_directories)} 个目录')
        
        # 为每个临时目录创建观察者
        for temp_name, temp_path in self.temp_directories:
            if os.path.exists(temp_path):
                try:
                    observer = Observer()
                    event_handler = TempFileEventHandler(self.logger, temp_name)
                    
                    observer.schedule(event_handler, temp_path, recursive=True)
                    observer.start()
                    self.observers.append((observer, temp_name, temp_path))
                    
                    self.logger.log_event('TEMP_FILES', 'Monitor', 'Directory Added', 
                                        f'开始监控临时目录: {temp_name} ({temp_path})')
                    
                except Exception as e:
                    self.logger.log_event('TEMP_FILES', 'Monitor', 'Error', 
                                        f'无法监控临时目录 {temp_name} ({temp_path}): {e}', 'ERROR')
            else:
                self.logger.log_event('TEMP_FILES', 'Monitor', 'Warning', 
                                    f'临时目录不存在: {temp_name} ({temp_path})', 'WARNING')
        
        # 启动定期清理线程
        self.start_cleanup_thread()
    
    def stop_monitoring(self):
        """停止监控临时文件"""
        self.monitoring = False
        
        # 停止所有观察者
        for observer, temp_name, temp_path in self.observers:
            try:
                observer.stop()
                observer.join(timeout=5)
            except Exception as e:
                self.logger.log_event('TEMP_FILES', 'Monitor', 'Error', 
                                    f'停止观察者失败 {temp_name}: {e}', 'ERROR')
        
        self.observers.clear()
        
        # 停止清理线程
        if self.cleanup_thread and self.cleanup_thread.is_alive():
            self.cleanup_thread.join(timeout=5)
        
        self.logger.log_event('TEMP_FILES', 'Monitor', 'Stopped', '临时文件监控已停止')
    
    def start_cleanup_thread(self):
        """启动定期清理线程"""
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
    
    def _cleanup_loop(self):
        """清理循环"""
        while self.monitoring:
            try:
                self._analyze_temp_directories()
                time.sleep(self.cleanup_interval)
            except Exception as e:
                self.logger.log_event('TEMP_FILES', 'Cleanup', 'Error', 
                                    f'清理循环错误: {e}', 'ERROR')
                time.sleep(60)
    
    def _analyze_temp_directories(self):
        """分析临时目录"""
        for temp_name, temp_path in self.temp_directories:
            if not os.path.exists(temp_path):
                continue
            
            try:
                total_size = 0
                file_count = 0
                old_files = []
                
                current_time = time.time()
                
                for root, dirs, files in os.walk(temp_path):
                    for file in files:
                        try:
                            file_path = os.path.join(root, file)
                            file_stat = os.stat(file_path)
                            file_size = file_stat.st_size
                            file_age = current_time - file_stat.st_mtime
                            
                            total_size += file_size
                            file_count += 1
                            
                            # 检查超过7天的文件
                            if file_age > 7 * 24 * 3600:  # 7天
                                old_files.append((file_path, file_age, file_size))
                            
                        except Exception:
                            continue
                
                # 记录统计信息
                self.logger.log_event('TEMP_FILES', 'Analysis', temp_name, 
                                    f'文件数: {file_count}, 总大小: {total_size / 1024 / 1024:.2f} MB, '
                                    f'旧文件数: {len(old_files)}')
                
                # 如果临时目录过大，发出警告
                if total_size > 1024 * 1024 * 1024:  # 1GB
                    self.logger.log_event('TEMP_FILES', 'Warning', temp_name, 
                                        f'临时目录过大: {total_size / 1024 / 1024 / 1024:.2f} GB', 
                                        'WARNING')
                
            except Exception as e:
                self.logger.log_event('TEMP_FILES', 'Analysis', 'Error', 
                                    f'分析临时目录失败 {temp_name}: {e}', 'ERROR')
    
    def get_temp_directory_info(self):
        """获取临时目录信息"""
        info = []
        
        for temp_name, temp_path in self.temp_directories:
            if os.path.exists(temp_path):
                try:
                    total_size = 0
                    file_count = 0
                    
                    for root, dirs, files in os.walk(temp_path):
                        for file in files:
                            try:
                                file_path = os.path.join(root, file)
                                total_size += os.path.getsize(file_path)
                                file_count += 1
                            except Exception:
                                continue
                    
                    info.append({
                        'name': temp_name,
                        'path': temp_path,
                        'file_count': file_count,
                        'total_size': total_size,
                        'exists': True
                    })
                    
                except Exception as e:
                    info.append({
                        'name': temp_name,
                        'path': temp_path,
                        'error': str(e),
                        'exists': True
                    })
            else:
                info.append({
                    'name': temp_name,
                    'path': temp_path,
                    'exists': False
                })
        
        return info
    
    def get_monitoring_status(self):
        """获取监控状态"""
        return {
            'monitoring': self.monitoring,
            'temp_directories': len(self.temp_directories),
            'active_observers': len(self.observers),
            'stats': self.stats.copy()
        }
